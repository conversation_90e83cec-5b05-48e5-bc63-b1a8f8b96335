"""Core centering algorithms for photo centering."""

import cv2
import numpy as np
from typing import Dict, Any, Tu<PERSON>, Optional, List
from dataclasses import dataclass

from ..utils.logger import get_logger
from ..utils.config import Config


@dataclass
class CenteringResult:
    """Result of centering operation."""
    cropped_image: np.ndarray
    crop_box: Tuple[int, int, int, int]  # (x1, y1, x2, y2)
    subject_center: Tuple[int, int]
    target_center: Tuple[int, int]
    confidence: float
    method_used: str


class PhotoCenterer:
    """Core photo centering functionality."""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize photo centerer.
        
        Args:
            config: Configuration object. If None, creates default config.
        """
        self.config = config or Config()
        self.logger = get_logger(__name__)
    
    def center_subject(
        self,
        image: np.ndarray,
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]] = None
    ) -> CenteringResult:
        """Center the detected subject in the image.

        Args:
            image: Input image as numpy array (8-bit or 16-bit)
            detection: Detection dictionary from human detector
            target_size: Target output size (width, height). If None, uses original size

        Returns:
            CenteringResult with cropped image and metadata (preserves original bit depth)
        """
        method = self.config.get('centering.method', 'keypoint_based')

        if method == 'face_chest_based' and detection.get('keypoints'):
            return self._center_by_face_chest(image, detection, target_size)
        elif method == 'keypoint_based' and detection.get('keypoints'):
            return self._center_by_keypoints(image, detection, target_size)
        elif method == 'bbox_based' or not detection.get('keypoints'):
            return self._center_by_bbox(image, detection, target_size)
        elif method == 'center_of_mass':
            return self._center_by_mass(image, detection, target_size)
        else:
            # Fallback to bbox method
            return self._center_by_bbox(image, detection, target_size)

    def center_two_people(
        self,
        image: np.ndarray,
        detections: List[Dict[str, Any]],
        target_size: Optional[Tuple[int, int]] = None
    ) -> CenteringResult:
        """Center based on the midpoint between two people.

        Args:
            image: Input image as numpy array (8-bit or 16-bit)
            detections: List of detection dictionaries (should contain at least 2 people)
            target_size: Target output size (width, height). If None, uses original size

        Returns:
            CenteringResult with cropped image and metadata (preserves original bit depth)
        """
        if len(detections) < 2:
            self.logger.warning("Two-person centering requested but less than 2 people detected, using single person centering")
            return self.center_subject(image, detections[0] if detections else {}, target_size)

        # Get the two most centered people
        image_height, image_width = image.shape[:2]
        image_center_x = image_width / 2
        image_center_y = image_height / 2

        # Calculate distance from image center for each detection
        def distance_from_center(detection):
            center = detection.get('center', (0, 0))
            return np.sqrt((center[0] - image_center_x)**2 + (center[1] - image_center_y)**2)

        # Sort detections by distance from center and take the two closest
        sorted_detections = sorted(detections, key=distance_from_center)
        person1, person2 = sorted_detections[0], sorted_detections[1]

        self.logger.debug(f"Two-person centering: selected people at {person1['center']} and {person2['center']}")

        # Calculate midpoint between the two people using the configured centering method
        method = self.config.get('centering.method', 'face_chest_based')

        center1 = self._get_person_center(person1, method)
        center2 = self._get_person_center(person2, method)

        # Calculate midpoint
        midpoint_x = int((center1[0] + center2[0]) / 2)
        midpoint_y = int((center1[1] + center2[1]) / 2)
        midpoint = (midpoint_x, midpoint_y)

        self.logger.debug(f"Two-person midpoint: {midpoint} (from {center1} and {center2})")

        # Create a combined detection for centering purposes
        combined_detection = {
            'bbox': self._get_combined_bbox([person1, person2]),
            'confidence': min(person1['confidence'], person2['confidence']),
            'center': midpoint,
            'keypoints': {}  # We'll use the midpoint directly
        }

        return self._perform_centering(
            image, midpoint, combined_detection, target_size, f'two_people_{method}'
        )

    def _center_by_face_chest(
        self,
        image: np.ndarray,
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]] = None
    ) -> CenteringResult:
        """Center subject based on face, chest, and hip keypoints with weighted priority.

        Args:
            image: Input image
            detection: Detection with keypoints
            target_size: Target output size

        Returns:
            CenteringResult
        """
        keypoints = detection.get('keypoints', {})
        if not keypoints:
            # Fallback to bbox method if no keypoints
            return self._center_by_bbox(image, detection, target_size)

        # Define face, chest, and hip keypoints
        face_keypoints = ['nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear']
        chest_keypoints = ['left_shoulder', 'right_shoulder']
        hip_keypoints = ['left_hip', 'right_hip']

        # Collect available face, chest, and hip points
        face_points = []
        chest_points = []
        hip_points = []

        for kp_name in face_keypoints:
            if kp_name in keypoints:
                face_points.append(keypoints[kp_name])

        for kp_name in chest_keypoints:
            if kp_name in keypoints:
                chest_points.append(keypoints[kp_name])

        for kp_name in hip_keypoints:
            if kp_name in keypoints:
                hip_points.append(keypoints[kp_name])

        # Get weights from config
        face_weight = self.config.get('centering.face_weight', 0.5)
        chest_weight = self.config.get('centering.chest_weight', 0.3)
        hip_weight = self.config.get('centering.hip_weight', 0.2)

        # Normalize weights to sum to 1.0
        total_weight = face_weight + chest_weight + hip_weight
        if total_weight > 0:
            face_weight /= total_weight
            chest_weight /= total_weight
            hip_weight /= total_weight

        weighted_points = []
        weights = []

        # Add face points
        for point in face_points:
            weighted_points.append(point)
            weights.append(face_weight / len(face_points) if face_points else 0)

        # Add chest points
        for point in chest_points:
            weighted_points.append(point)
            weights.append(chest_weight / len(chest_points) if chest_points else 0)

        # Add hip points
        for point in hip_points:
            weighted_points.append(point)
            weights.append(hip_weight / len(hip_points) if hip_points else 0)

        if not weighted_points:
            # No face, chest, or hip keypoints available, fallback to all keypoints
            return self._center_by_keypoints(image, detection, target_size)

        # Calculate weighted center
        weighted_points = np.array(weighted_points)
        weights = np.array(weights)
        subject_center = np.average(weighted_points, axis=0, weights=weights)
        subject_center = tuple(map(int, subject_center))

        self.logger.debug(f"Face/chest/hip centering: {len(face_points)} face, {len(chest_points)} chest, {len(hip_points)} hip points")

        return self._perform_centering(
            image, subject_center, detection, target_size, 'face_chest_based'
        )

    def _center_by_keypoints(
        self, 
        image: np.ndarray, 
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]] = None
    ) -> CenteringResult:
        """Center subject based on keypoints (preferred method).
        
        Args:
            image: Input image
            detection: Detection with keypoints
            target_size: Target output size
            
        Returns:
            CenteringResult
        """
        keypoints = detection['keypoints']
        
        # Calculate subject center based on important keypoints
        important_points = []
        
        # Head area (highest priority)
        head_points = ['nose', 'left_eye', 'right_eye']
        head_coords = [keypoints[kp] for kp in head_points if kp in keypoints]
        
        if head_coords:
            head_center = np.mean(head_coords, axis=0)
            important_points.append(head_center)
        
        # Shoulder area (medium priority)
        shoulder_points = ['left_shoulder', 'right_shoulder']
        shoulder_coords = [keypoints[kp] for kp in shoulder_points if kp in keypoints]
        
        if shoulder_coords:
            shoulder_center = np.mean(shoulder_coords, axis=0)
            important_points.append(shoulder_center)
        
        # Hip area (lower priority)
        hip_points = ['left_hip', 'right_hip']
        hip_coords = [keypoints[kp] for kp in hip_points if kp in keypoints]
        
        if hip_coords:
            hip_center = np.mean(hip_coords, axis=0)
            important_points.append(hip_center)
        
        if not important_points:
            # Fallback to bbox method if no keypoints available
            return self._center_by_bbox(image, detection, target_size)
        
        # Calculate weighted center (head gets more weight)
        weights = [3.0, 2.0, 1.0][:len(important_points)]  # Head, shoulders, hips
        subject_center = np.average(important_points, axis=0, weights=weights)
        subject_center = tuple(map(int, subject_center))
        
        return self._perform_centering(
            image, subject_center, detection, target_size, 'keypoint_based'
        )
    
    def _center_by_bbox(
        self, 
        image: np.ndarray, 
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]] = None
    ) -> CenteringResult:
        """Center subject based on bounding box.
        
        Args:
            image: Input image
            detection: Detection with bounding box
            target_size: Target output size
            
        Returns:
            CenteringResult
        """
        bbox = detection['bbox']
        x1, y1, x2, y2 = bbox
        
        # Use center of bounding box
        subject_center = ((x1 + x2) // 2, (y1 + y2) // 2)
        
        return self._perform_centering(
            image, subject_center, detection, target_size, 'bbox_based'
        )
    
    def _center_by_mass(
        self, 
        image: np.ndarray, 
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]] = None
    ) -> CenteringResult:
        """Center subject based on center of mass within bounding box.
        
        Args:
            image: Input image
            detection: Detection with bounding box
            target_size: Target output size
            
        Returns:
            CenteringResult
        """
        bbox = detection['bbox']
        x1, y1, x2, y2 = bbox
        
        # Extract region of interest
        roi = image[y1:y2, x1:x2]

        # Convert to grayscale for center of mass calculation
        # Handle both 8-bit and 16-bit images
        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

        # Normalize to 8-bit for moment calculation if needed (moments work better with 8-bit)
        if gray_roi.dtype == np.uint16:
            # Use proper tone mapping instead of simple division
            gray_roi_normalized = self._convert_16bit_to_8bit_grayscale(gray_roi)
        else:
            gray_roi_normalized = gray_roi
        
        # Calculate center of mass using normalized grayscale image
        moments = cv2.moments(gray_roi_normalized)
        if moments['m00'] != 0:
            cx = int(moments['m10'] / moments['m00']) + x1
            cy = int(moments['m01'] / moments['m00']) + y1
            subject_center = (cx, cy)
        else:
            # Fallback to bbox center
            subject_center = ((x1 + x2) // 2, (y1 + y2) // 2)
        
        return self._perform_centering(
            image, subject_center, detection, target_size, 'center_of_mass'
        )

    def _get_person_center(self, detection: Dict[str, Any], method: str) -> Tuple[int, int]:
        """Get the center point of a person using the specified method.

        Args:
            detection: Detection dictionary
            method: Centering method to use

        Returns:
            Center coordinates (x, y)
        """
        keypoints = detection.get('keypoints', {})

        if method == 'face_chest_based' and keypoints:
            return self._get_face_chest_center(keypoints)
        elif method == 'keypoint_based' and keypoints:
            return self._get_keypoint_center(keypoints)
        elif method == 'center_of_mass':
            # For center of mass, we'd need the image, so fall back to bbox
            return self._get_bbox_center(detection)
        else:
            # Default to bbox center
            return self._get_bbox_center(detection)

    def _get_face_chest_center(self, keypoints: Dict[str, Tuple[int, int]]) -> Tuple[int, int]:
        """Calculate center based on face and chest keypoints."""
        face_keypoints = ['nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear']
        chest_keypoints = ['left_shoulder', 'right_shoulder']
        hip_keypoints = ['left_hip', 'right_hip']

        face_points = [keypoints[kp] for kp in face_keypoints if kp in keypoints]
        chest_points = [keypoints[kp] for kp in chest_keypoints if kp in keypoints]
        hip_points = [keypoints[kp] for kp in hip_keypoints if kp in keypoints]

        # Get weights from config
        face_weight = self.config.get('centering.face_weight', 0.5)
        chest_weight = self.config.get('centering.chest_weight', 0.3)
        hip_weight = self.config.get('centering.hip_weight', 0.2)

        weighted_points = []
        weights = []

        for point in face_points:
            weighted_points.append(point)
            weights.append(face_weight / len(face_points) if face_points else 0)

        for point in chest_points:
            weighted_points.append(point)
            weights.append(chest_weight / len(chest_points) if chest_points else 0)

        for point in hip_points:
            weighted_points.append(point)
            weights.append(hip_weight / len(hip_points) if hip_points else 0)

        if weighted_points:
            weighted_points = np.array(weighted_points)
            weights = np.array(weights)
            center = np.average(weighted_points, axis=0, weights=weights)
            return tuple(map(int, center))
        else:
            # Fallback to detection center if no keypoints
            return (0, 0)

    def _get_keypoint_center(self, keypoints: Dict[str, Tuple[int, int]]) -> Tuple[int, int]:
        """Calculate center based on important keypoints."""
        important_points = []

        # Head area (highest priority)
        head_points = ['nose', 'left_eye', 'right_eye']
        head_coords = [keypoints[kp] for kp in head_points if kp in keypoints]

        if head_coords:
            head_center = np.mean(head_coords, axis=0)
            important_points.append(head_center)

        # Shoulder area (medium priority)
        shoulder_points = ['left_shoulder', 'right_shoulder']
        shoulder_coords = [keypoints[kp] for kp in shoulder_points if kp in keypoints]

        if shoulder_coords:
            shoulder_center = np.mean(shoulder_coords, axis=0)
            important_points.append(shoulder_center)

        # Hip area (lower priority)
        hip_points = ['left_hip', 'right_hip']
        hip_coords = [keypoints[kp] for kp in hip_points if kp in keypoints]

        if hip_coords:
            hip_center = np.mean(hip_coords, axis=0)
            important_points.append(hip_center)

        if important_points:
            # Calculate weighted center (head gets more weight)
            weights = [3.0, 2.0, 1.0][:len(important_points)]
            center = np.average(important_points, axis=0, weights=weights)
            return tuple(map(int, center))
        else:
            return (0, 0)

    def _get_bbox_center(self, detection: Dict[str, Any]) -> Tuple[int, int]:
        """Get center of bounding box."""
        bbox = detection['bbox']
        x1, y1, x2, y2 = bbox
        return ((x1 + x2) // 2, (y1 + y2) // 2)

    def _get_combined_bbox(self, detections: List[Dict[str, Any]]) -> List[int]:
        """Get combined bounding box that encompasses all detections."""
        if not detections:
            return [0, 0, 0, 0]

        min_x1 = min(det['bbox'][0] for det in detections)
        min_y1 = min(det['bbox'][1] for det in detections)
        max_x2 = max(det['bbox'][2] for det in detections)
        max_y2 = max(det['bbox'][3] for det in detections)

        return [min_x1, min_y1, max_x2, max_y2]

    def _perform_centering(
        self,
        image: np.ndarray,
        subject_center: Tuple[int, int],
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]],
        method: str
    ) -> CenteringResult:
        """Perform the actual centering operation.

        Args:
            image: Input image
            subject_center: Center point of the subject
            detection: Original detection data
            target_size: Target output size
            method: Method used for centering

        Returns:
            CenteringResult
        """
        height, width = image.shape[:2]
        subject_x, subject_y = subject_center

        # Get target position from config
        target_pos = self.config.target_position
        margin_ratio = self.config.margin_ratio

        self.logger.debug(f"Centering: image={width}x{height}, subject=({subject_x},{subject_y}), target_pos={target_pos}")

        # Determine output size
        if target_size is None:
            output_width, output_height = width, height
        else:
            output_width, output_height = target_size

        # Calculate target center in output image
        target_center_x = int(output_width * target_pos[0])
        target_center_y = int(output_height * target_pos[1])

        self.logger.debug(f"Target center in output: ({target_center_x},{target_center_y}) for {output_width}x{output_height}")

        # Calculate crop box to center subject at target position
        crop_x1 = subject_x - target_center_x
        crop_y1 = subject_y - target_center_y
        crop_x2 = crop_x1 + output_width
        crop_y2 = crop_y1 + output_height

        self.logger.debug(f"Initial crop box: ({crop_x1},{crop_y1}) to ({crop_x2},{crop_y2})")

        # Ensure crop box is within image bounds with margin
        bbox = detection['bbox']
        subject_width = bbox[2] - bbox[0]
        subject_height = bbox[3] - bbox[1]

        # Calculate minimum margins
        min_margin_x = int(subject_width * margin_ratio)
        min_margin_y = int(subject_height * margin_ratio)

        # Adjust crop box if it goes outside image bounds
        if crop_x1 < min_margin_x:
            offset = min_margin_x - crop_x1
            crop_x1 += offset
            crop_x2 += offset
            self.logger.debug(f"Adjusted crop box right by {offset} pixels (left margin)")

        if crop_y1 < min_margin_y:
            offset = min_margin_y - crop_y1
            crop_y1 += offset
            crop_y2 += offset
            self.logger.debug(f"Adjusted crop box down by {offset} pixels (top margin)")

        if crop_x2 > width - min_margin_x:
            offset = crop_x2 - (width - min_margin_x)
            crop_x1 -= offset
            crop_x2 -= offset
            self.logger.debug(f"Adjusted crop box left by {offset} pixels (right margin)")

        if crop_y2 > height - min_margin_y:
            offset = crop_y2 - (height - min_margin_y)
            crop_y1 -= offset
            crop_y2 -= offset
            self.logger.debug(f"Adjusted crop box up by {offset} pixels (bottom margin)")

        # Final bounds check
        crop_x1 = max(0, crop_x1)
        crop_y1 = max(0, crop_y1)
        crop_x2 = min(width, crop_x2)
        crop_y2 = min(height, crop_y2)

        self.logger.debug(f"Final crop box: ({crop_x1},{crop_y1}) to ({crop_x2},{crop_y2})")

        # Crop the image
        cropped_image = image[crop_y1:crop_y2, crop_x1:crop_x2]

        # Resize if target size specified and different from crop size
        if target_size and (cropped_image.shape[1] != output_width or cropped_image.shape[0] != output_height):
            cropped_image = cv2.resize(cropped_image, (output_width, output_height), interpolation=cv2.INTER_LANCZOS4)

        # Calculate final target center in cropped image
        final_target_center = (target_center_x, target_center_y)

        # Calculate confidence based on how well we achieved the centering
        actual_subject_x = subject_x - crop_x1
        actual_subject_y = subject_y - crop_y1

        if target_size:
            # Scale coordinates if image was resized
            scale_x = output_width / (crop_x2 - crop_x1)
            scale_y = output_height / (crop_y2 - crop_y1)
            actual_subject_x = int(actual_subject_x * scale_x)
            actual_subject_y = int(actual_subject_y * scale_y)

        # Calculate distance from target
        distance = np.sqrt((actual_subject_x - target_center_x)**2 + (actual_subject_y - target_center_y)**2)
        max_distance = np.sqrt(output_width**2 + output_height**2)
        confidence = max(0.0, 1.0 - (distance / max_distance))

        self.logger.debug(f"Centering completed: method={method}, confidence={confidence:.3f}")

        return CenteringResult(
            cropped_image=cropped_image,
            crop_box=(crop_x1, crop_y1, crop_x2, crop_y2),
            subject_center=subject_center,
            target_center=final_target_center,
            confidence=confidence,
            method_used=method
        )

    def visualize_centering(self, result: CenteringResult) -> np.ndarray:
        """Visualize the centering result.

        Args:
            result: CenteringResult to visualize

        Returns:
            Image with centering visualization
        """
        vis_image = result.cropped_image.copy()

        # Draw target center with more visible styling
        target_x, target_y = result.target_center

        # Draw larger, more visible target circle - using bright green for high visibility
        cv2.circle(vis_image, (target_x, target_y), 15, (0, 255, 0), 3)
        cv2.circle(vis_image, (target_x, target_y), 8, (0, 255, 0), -1)

        # Draw crosshairs at target center
        cv2.line(vis_image, (target_x - 30, target_y), (target_x + 30, target_y), (0, 255, 0), 2)
        cv2.line(vis_image, (target_x, target_y - 30), (target_x, target_y + 30), (0, 255, 0), 2)

        # Add target label with background
        label_text = "TARGET"
        label_size = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        label_x = target_x + 20
        label_y = target_y - 10

        # Draw background rectangle for label
        cv2.rectangle(vis_image, (label_x - 2, label_y - label_size[1] - 2),
                     (label_x + label_size[0] + 2, label_y + 2), (0, 0, 0), -1)
        cv2.putText(vis_image, label_text, (label_x, label_y),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # Add confidence and method text with backgrounds
        info_texts = [
            f"Confidence: {result.confidence:.3f}",
            f"Method: {result.method_used}",
            f"Size: {vis_image.shape[1]}x{vis_image.shape[0]}"
        ]

        for i, text in enumerate(info_texts):
            y_pos = 30 + i * 30
            text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]

            # Draw background rectangle
            cv2.rectangle(vis_image, (8, y_pos - text_size[1] - 2),
                         (12 + text_size[0], y_pos + 2), (0, 0, 0), -1)
            cv2.putText(vis_image, text, (10, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        return vis_image

    def visualize_original_detection(
        self,
        image: np.ndarray,
        detection: Dict[str, Any],
        result: CenteringResult
    ) -> np.ndarray:
        """Visualize the original detection and crop area.

        Args:
            image: Original image
            detection: Detection data
            result: Centering result

        Returns:
            Image with original detection and crop visualization
        """
        vis_image = image.copy()
        bbox = detection['bbox']
        keypoints = detection.get('keypoints', {})

        # Draw bounding box with thick, bright border - using bright magenta for high visibility
        cv2.rectangle(vis_image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (255, 0, 255), 4)

        # Draw confidence label
        label = f"Person: {detection['confidence']:.3f}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
        label_x, label_y = bbox[0], bbox[1] - 10

        # Background for label
        cv2.rectangle(vis_image, (label_x - 2, label_y - label_size[1] - 2),
                     (label_x + label_size[0] + 2, label_y + 2), (0, 0, 0), -1)
        cv2.putText(vis_image, label, (label_x, label_y),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 255), 2)

        # Draw keypoints if available
        if keypoints:
            for name, (x, y) in keypoints.items():
                # Use bright, highly visible colors for different keypoint types
                if 'eye' in name or 'nose' in name:
                    color = (255, 20, 147)  # Hot Pink for face - highly visible
                elif 'shoulder' in name:
                    color = (0, 255, 0)  # Bright Green for shoulders
                elif 'hip' in name:
                    color = (0, 165, 255)  # Bright Orange for hips
                else:
                    color = (0, 255, 255)  # Bright Cyan for others

                cv2.circle(vis_image, (x, y), 5, color, -1)
                cv2.circle(vis_image, (x, y), 7, (255, 255, 255), 2)

        # Draw crop area - using bright green for high visibility
        crop_x1, crop_y1, crop_x2, crop_y2 = result.crop_box
        cv2.rectangle(vis_image, (crop_x1, crop_y1), (crop_x2, crop_y2), (0, 255, 0), 3)

        # Draw subject center - using bright cyan for high visibility
        subject_x, subject_y = result.subject_center
        cv2.circle(vis_image, (subject_x, subject_y), 10, (255, 255, 255), 3)
        cv2.circle(vis_image, (subject_x, subject_y), 6, (0, 255, 255), -1)

        # Add legend
        legend_texts = [
            "Magenta: Detection Box",
            "Green: Crop Area",
            "White/Cyan: Subject Center",
            f"Crop: {crop_x2-crop_x1}x{crop_y2-crop_y1}"
        ]

        for i, text in enumerate(legend_texts):
            y_pos = vis_image.shape[0] - 120 + i * 30
            text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

            # Background for legend
            cv2.rectangle(vis_image, (8, y_pos - text_size[1] - 2),
                         (12 + text_size[0], y_pos + 2), (0, 0, 0), -1)
            cv2.putText(vis_image, text, (10, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return vis_image

    def _convert_16bit_to_8bit_grayscale(self, image_16bit: np.ndarray) -> np.ndarray:
        """Convert 16-bit grayscale image to 8-bit with proper tone mapping.

        Args:
            image_16bit: 16-bit input grayscale image

        Returns:
            8-bit grayscale image with proper tone mapping
        """
        # Method: Percentile-based scaling (more robust for RAW images)
        # Find the 1st and 99th percentiles to avoid extreme values
        p1 = np.percentile(image_16bit, 1)
        p99 = np.percentile(image_16bit, 99)

        # Clip and scale to 0-255 range
        clipped = np.clip(image_16bit, p1, p99)
        scaled = ((clipped - p1) / (p99 - p1) * 255).astype(np.uint8)

        return scaled
